<template>
	<page-container :isShowNav="false" bgColorPage="#FAFAFA">
		<image
			:src="backgroundImage"
			:style="backgroundImageStyle"
			mode="aspectFill"
			class="w-100% h-360 fixed top-0 z-10"
		/>
		<custom-nav bg-color="unset" title="" :is-back="true"> </custom-nav>
		<view class="content-wrapper px-24">
			<view class="server-info sticky top-200 z-10">
				<view class="relative flex justify-between">
					<view class="flex flex-col">
						<view class="text-36 font-bold">宝塔面板-AAA-BBB</view>
						<view class="text-24 py-16">IP：************* | Opencloudos 9</view>
						<view class="text-24 flex items-center">
							<view class="w-16 h-16 bg-#20a50a rd-50%"></view>
							<text class="text-24 pl-16">持续运行3天</text>
						</view>
					</view>
					<image
						src="@/static/index/server-bg.png"
						mode="aspectFit"
						class="absolute -top-120 -right-80 w-460 h-460"
						:style="serverBgStyle"
					></image>
				</view>
			</view>
			<view class="detail mt-68 z-1">
				<function-list
					title="功能"
					:function-list="basicFunctionList"
					:show-edit="true"
					:columns="5"
					@itemClick="() => {}"
					@editClick="() => {}"
				/>
				<function-list
					class="my-24"
					title="插件"
					:function-list="pluginFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="() => {}"
					@editClick="() => {}"
				/>
				<statusInfo title="负载" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">1分钟</text>
									<text class="text-24 font-800">0.54%</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">5分钟</text>
									<text class="text-24 font-800">0.54%</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">15分钟</text>
									<text class="text-24 font-800">0.54%</text>
								</view>
							</view>
							<view class="">
								<MetricProgressBar
									mode="vertical"
									:value="40"
									height="184rpx"
									thickness="100rpx"
									:server-status="true"
									:animated="true"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<ECharts
								canvas-id="load-chart-info"
								chart-type="line"
								:chart-data="loadChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>
				<statusInfo class="mt-24" title="CPU" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">核心</text>
									<text class="text-24 font-800">4</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">已用</text>
									<text class="text-24 font-800">0.54%</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">空闲</text>
									<text class="text-24 font-800">99%</text>
								</view>
							</view>
							<view class="">
								<ECharts
									canvas-id="cpu-chart"
									chart-type="gauge"
									:chart-data="getBaseChartConfig(chartMap['cpu']?.val, 'CPU')"
									:height="140"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
								<text class="text-28 font-800">基础信息</text>
								<text class="text-24 text-secondary py-8"
									>Intel(R) Xeon(R) Platinum 8361HC CPU @ 2.60GHz</text
								>
								<text class="text-24 text-secondary">1个物理CPU，4个物理核心，4个逻辑核心</text>
							</view>
							<ECharts
								canvas-id="cpu-chart-info"
								chart-type="line"
								:chart-data="cpuChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>
				<statusInfo class="mt-24" title="内存" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">总计</text>
									<text class="text-24 font-800">8GB</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">已用</text>
									<text class="text-24 font-800">4GB</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">空闲</text>
									<text class="text-24 font-800">4GB</text>
								</view>
							</view>
							<view class="">
								<ECharts
									canvas-id="memory-chart"
									chart-type="gauge"
									:chart-data="getBaseChartConfig(chartMap['memory']?.val, '内存')"
									:height="140"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<ECharts
								canvas-id="memory-chart-info"
								chart-type="line"
								:chart-data="memoryChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>

				<statusInfo class="mt-24" title="磁盘" expanded-text="收起" collapsed-text="详情" defaultExpanded>
					<template #desc>
						<text class="text-24 text-secondary"> 若有多个磁盘，可左右滑动查看 </text>
					</template>
					<template #basic>
						<swiper class="server-item h-100">
							<swiper-item>
								<view class="flex items-center justify-between">
									<view class="flex items-center justify-between w-60%">
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">总计</text>
											<text class="text-24 font-800">100GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">已用</text>
											<text class="text-24 font-800">90GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">空闲</text>
											<text class="text-24 font-800">10GB</text>
										</view>
									</view>
									<view class="w-40% ml-80 mt-24">
										<MetricProgressBar
											class="z-1"
											mode="horizontal"
											:value="90"
											width="100%"
											thickness="16rpx"
											:server-status="true"
											:animated="true"
											text-position="top-right"
										/>
									</view>
								</view>
							</swiper-item>
							<swiper-item>
								<view class="flex items-center justify-between">
									<view class="flex items-center justify-between w-60%">
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">总计</text>
											<text class="text-24 font-800">50GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">已用</text>
											<text class="text-24 font-800">25GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">空闲</text>
											<text class="text-24 font-800">25GB</text>
										</view>
									</view>
									<view class="w-40% ml-80 mt-24">
										<MetricProgressBar
											mode="horizontal"
											:value="66"
											width="100%"
											thickness="16rpx"
											:server-status="true"
											:animated="true"
											text-position="top-right"
										/>
									</view>
								</view>
							</swiper-item>
						</swiper>
					</template>

					<template #details>
						<view class="details-info">
							<ECharts
								canvas-id="disk-chart-info"
								chart-type="line"
								:chart-data="diskChartData"
								:opts="getLoadChartStyle()"
								:height="500"
							/>
						</view>
					</template>
				</statusInfo>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, computed } from 'vue';
	import { onPageScroll } from '@dcloudio/uni-app';
	import CustomNav from '@/components/customNav/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import ECharts from '@/components/ECharts/index.vue';
	import bgLight from '@/static/index/bg-light.png';
	import FunctionList from './functionList.vue';
	import StatusInfo from './statusInfo.vue';
	import MetricProgressBar from '@/components/MetricProgressBar/index.vue';
	import {
		getLoadChartStyle,
		loadChartData,
		cpuChartData,
		getBaseChartConfig,
		chartMap,
		memoryChartData,
		diskChartData,
	} from './useController';
	const basicFunctionList = ref([
		{
			label: '网站',
			image: '/static/index/web_site.png',
		},
		{
			label: '数据库',
			image: '/static/index/database.png',
		},
		{
			label: '系统防火墙',
			image: '/static/index/firewall.png',
		},
		{
			label: '终端',
			image: '/static/index/terminal.png',
		},
		{
			label: '安全风险',
			image: '/static/index/security_risk.png',
		},
		{
			label: '监控',
			image: '/static/index/system_monitor.png',
		},
		{
			label: 'SSH管理',
			image: '/static/index/ssh_manager.png',
		},
		{
			label: '文件管理',
			image: '/static/index/file_manager.png',
		},
		{
			label: '计划任务',
			image: '/static/index/cron_task.png',
		},
		{
			label: '日志',
			image: '/static/index/log_manager.png',
		},
	]);

	const pluginFunctionList = ref([
		{
			label: '站点监控',
			image: '/static/index/web_monitor.png',
		},
		{
			label: '系统加固',
			image: '/static/index/os_hardening.png',
		},
		{
			label: 'nginx防火墙',
			image: '/static/index/nginx_waf.png',
		},
		{
			label: '防篡改',
			image: '/static/index/tamper_protection.png',
		},
	]);

	const backgroundImage = ref(bgLight);
	const backgroundImageStyle = ref({
		backgroundImage: `url(${backgroundImage.value})`,
		backgroundSize: 'cover',
		backgroundPosition: 'center',
	});

	// 背景图片缩放相关变量
	const serverBgScale = ref(1); // 初始缩放比例为1
	const maxScrollDistance = 300; // 最大滚动距离，超过此距离缩放效果不再变化
	const minScale = 0.6; // 最小缩放比例

	// 计算背景图片的动态样式
	const serverBgStyle = computed(() => {
		return {
			transform: `scale(${serverBgScale.value})`,
			transition: 'transform 0.1s ease-out', // 添加平滑过渡效果
		};
	});

	// 页面滚动事件处理
	onPageScroll((e) => {
		const scrollTop = e.scrollTop;

		// 计算缩放比例：滚动距离越大，缩放比例越小
		// 使用线性插值计算：从1缩放到minScale
		const scrollRatio = Math.min(scrollTop / maxScrollDistance, 1);
		const newScale = 1 - (1 - minScale) * scrollRatio;

		serverBgScale.value = Math.max(newScale, minScale);
		console.log(serverBgScale.value);
	});
</script>
<style lang="scss" scoped></style>
